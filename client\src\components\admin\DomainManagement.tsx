import React, { useState, useEffect } from 'react';
import {
  Box,
  Table as ChakraTable,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Switch,
  Button,
  useToast,
  Stat,
  StatLabel,
  StatNumber,
  StatGroup,
  Card,
  CardBody,
} from '@chakra-ui/react';

interface Domain {
  id: string;
  name: string;
  is_active: boolean;
  description: string;
  domain_learning_paths: { count: number };
  domain_subscription_plans: { count: number };
}

interface DomainStats {
  total_users: number;
  completed_users: number;
  average_completion: number;
}

export const DomainManagement: React.FC = () => {
  const [domains, setDomains] = useState<Domain[]>([]);
  const [selectedDomainStats, setSelectedDomainStats] = useState<DomainStats | null>(null);
  const toast = useToast();

  useEffect(() => {
    fetchDomains();
  }, []);

  const fetchDomains = async () => {
    try {
      const response = await fetch('/api/admin/domains');
      const data = await response.json();
      setDomains(data);
    } catch (error) {
      toast({
        title: 'Error fetching domains',
        status: 'error',
        duration: 3000,
      });
    }
  };

  const toggleDomainStatus = async (id: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/admin/domains/${id}/toggle`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_active: !currentStatus }),
      });
      
      if (response.ok) {
        setDomains(domains.map(domain => 
          domain.id === id ? { ...domain, is_active: !currentStatus } : domain
        ));
        toast({
          title: 'Domain status updated',
          status: 'success',
          duration: 2000,
        });
      }
    } catch (error) {
      toast({
        title: 'Error updating domain status',
        status: 'error',
        duration: 3000,
      });
    }
  };

  const fetchDomainStats = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/domains/${id}/stats`);
      const data = await response.json();
      setSelectedDomainStats(data);
    } catch (error) {
      toast({
        title: 'Error fetching domain stats',
        status: 'error',
        duration: 3000,
      });
    }
  };

  return (
    <Box p={4}>
      <Table variant="simple">
        <Thead>
          <Tr>
            <Th>Name</Th>
            <Th>Status</Th>
            <Th>Learning Paths</Th>
            <Th>Subscription Plans</Th>
            <Th>Actions</Th>
          </Tr>
        </Thead>
        <Tbody>
          {domains.map((domain) => (
            <Tr key={domain.id}>
              <Td>{domain.name}</Td>
              <Td>
                <Switch
                  isChecked={domain.is_active}
                  onChange={() => toggleDomainStatus(domain.id, domain.is_active)}
                />
              </Td>
              <Td>{domain.domain_learning_paths.count}</Td>
              <Td>{domain.domain_subscription_plans.count}</Td>
              <Td>
                <Button
                  size="sm"
                  onClick={() => fetchDomainStats(domain.id)}
                >
                  View Stats
                </Button>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>

      {selectedDomainStats && (
        <Card mt={4}>
          <CardBody>
            <StatGroup>
              <Stat>
                <StatLabel>Total Users</StatLabel>
                <StatNumber>{selectedDomainStats.total_users}</StatNumber>
              </Stat>
              <Stat>
                <StatLabel>Completed Users</StatLabel>
                <StatNumber>{selectedDomainStats.completed_users}</StatNumber>
              </Stat>
              <Stat>
                <StatLabel>Average Completion</StatLabel>
                <StatNumber>{selectedDomainStats.average_completion.toFixed(2)}%</StatNumber>
              </Stat>
            </StatGroup>
          </CardBody>
        </Card>
      )}
    </Box>
  );
};